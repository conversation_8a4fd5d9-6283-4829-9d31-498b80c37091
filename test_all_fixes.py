#!/usr/bin/env python3
"""
اختبار شامل لجميع الإصلاحات المطبقة
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_youtube_transcript_api():
    """اختبار youtube-transcript-api"""
    try:
        from youtube_transcript_api import YouTubeTranscriptApi
        logger.info("✅ youtube-transcript-api متوفر")
        return True
    except ImportError:
        logger.error("❌ youtube-transcript-api غير متوفر")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في youtube-transcript-api: {e}")
        return False

def test_gemini_api_manager():
    """اختبار مدير مفاتيح Gemini API"""
    try:
        from config.settings import google_api_manager
        
        if google_api_manager:
            # اختبار الحصول على مفتاح
            key = google_api_manager.get_key()
            logger.info(f"✅ مدير مفاتيح Gemini يعمل: {key[:10]}...")
            
            # اختبار عدد المفاتيح المتاحة
            available_count = google_api_manager.get_available_keys_count()
            logger.info(f"📊 عدد المفاتيح المتاحة: {available_count}")
            
            return True
        else:
            logger.error("❌ مدير مفاتيح Gemini غير متوفر")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في مدير مفاتيح Gemini: {e}")
        return False

def test_content_generator():
    """اختبار مولد المحتوى"""
    try:
        from modules.content_generator import ContentGenerator
        
        generator = ContentGenerator()
        logger.info("✅ مولد المحتوى متوفر")
        
        # اختبار وجود الدوال المحسنة
        if hasattr(generator, '_build_enhanced_article_prompt'):
            logger.info("✅ البرومبت المحسن متوفر")
        else:
            logger.warning("⚠️ البرومبت المحسن غير متوفر")
        
        if hasattr(generator, '_enhanced_quality_review'):
            logger.info("✅ نظام مراجعة الجودة المحسن متوفر")
        else:
            logger.warning("⚠️ نظام مراجعة الجودة المحسن غير متوفر")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في مولد المحتوى: {e}")
        return False

def test_content_optimizer():
    """اختبار محسن المحتوى"""
    try:
        from modules.content_optimizer import ContentOptimizer
        
        optimizer = ContentOptimizer()
        logger.info("✅ محسن المحتوى متوفر")
        
        # اختبار وجود الدوال الآمنة
        if hasattr(optimizer, 'safe_optimize_article_automatically'):
            logger.info("✅ الدالة الآمنة للتحسين متوفرة")
        else:
            logger.warning("⚠️ الدالة الآمنة للتحسين غير متوفرة")
        
        if hasattr(optimizer, '_safe_get_article_data'):
            logger.info("✅ دالة الحصول الآمن على البيانات متوفرة")
        else:
            logger.warning("⚠️ دالة الحصول الآمن على البيانات غير متوفرة")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في محسن المحتوى: {e}")
        return False

def test_youtube_analyzer():
    """اختبار محلل YouTube"""
    try:
        from modules.youtube_analyzer import YouTubeAnalyzer
        
        analyzer = YouTubeAnalyzer()
        logger.info("✅ محلل YouTube متوفر")
        
        # اختبار وجود الدالة الاحتياطية
        if hasattr(analyzer, '_fallback_transcript_extraction'):
            logger.info("✅ دالة استخراج النص الاحتياطية متوفرة")
        else:
            logger.warning("⚠️ دالة استخراج النص الاحتياطية غير متوفرة")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في محلل YouTube: {e}")
        return False

def test_gemini_error_handler():
    """اختبار معالج أخطاء Gemini"""
    try:
        from modules.gemini_error_handler import get_gemini_error_handler
        
        handler = get_gemini_error_handler()
        if handler:
            logger.info("✅ معالج أخطاء Gemini متوفر")
            return True
        else:
            logger.warning("⚠️ معالج أخطاء Gemini غير متوفر")
            return False
            
    except ImportError:
        logger.warning("⚠️ معالج أخطاء Gemini غير موجود")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في معالج أخطاء Gemini: {e}")
        return False

def main():
    """الدالة الرئيسية لاختبار جميع الإصلاحات"""
    logger.info("🚀 بدء اختبار شامل لجميع الإصلاحات...")
    
    tests = [
        ("YouTube Transcript API", test_youtube_transcript_api),
        ("مدير مفاتيح Gemini API", test_gemini_api_manager),
        ("مولد المحتوى", test_content_generator),
        ("محسن المحتوى", test_content_optimizer),
        ("محلل YouTube", test_youtube_analyzer),
        ("معالج أخطاء Gemini", test_gemini_error_handler),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 اختبار {test_name}...")
        try:
            if test_func():
                passed_tests += 1
                logger.info(f"✅ {test_name}: نجح")
            else:
                logger.warning(f"❌ {test_name}: فشل")
        except Exception as e:
            logger.error(f"❌ {test_name}: خطأ - {e}")
    
    # النتيجة النهائية
    logger.info(f"\n🎯 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        logger.info("✅ جميع الإصلاحات تعمل بشكل صحيح!")
        logger.info("🚀 يمكنك الآن تشغيل الوكيل بثقة")
    elif passed_tests >= total_tests * 0.8:
        logger.info("✅ معظم الإصلاحات تعمل بشكل صحيح!")
        logger.info("⚠️ بعض المكونات قد تحتاج مراجعة إضافية")
    else:
        logger.warning("⚠️ عدة مشاكل لا تزال موجودة")
        logger.info("📋 يرجى مراجعة الأخطاء أعلاه وإصلاحها")
    
    # إنشاء تقرير
    report = {
        "timestamp": str(logger.handlers[0].formatter.formatTime(logger.makeRecord("", 0, "", 0, "", (), None))),
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "success_rate": (passed_tests / total_tests) * 100,
        "status": "success" if passed_tests == total_tests else "partial" if passed_tests >= total_tests * 0.8 else "failed"
    }
    
    try:
        import json
        with open("test_fixes_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info("📊 تم إنشاء تقرير الاختبار: test_fixes_report.json")
    except Exception as e:
        logger.warning(f"⚠️ فشل في إنشاء التقرير: {e}")

if __name__ == "__main__":
    main()

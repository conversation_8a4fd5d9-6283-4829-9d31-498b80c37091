#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعدادات مفاتيح Gemini المتعددة
ضع مفاتيحك هنا بأمان
"""

import os
from typing import List, Dict

class GeminiKeysConfig:
    """إعدادات مفاتيح Gemini"""
    
    # ضع مفاتيح Gemini هنا
    # يمكنك الحصول على مفاتيح مجانية من: https://makersuite.google.com/app/apikey
    GEMINI_API_KEYS = [
        # المفتاح الأساسي
        os.getenv('GEMINI_API_KEY_1', ''),
        
        # مفاتيح إضافية للتناوب
        os.getenv('GEMINI_API_KEY_2', ''),
        os.getenv('GEMINI_API_KEY_3', ''),
        os.getenv('GEMINI_API_KEY_4', ''),
        os.getenv('GEMINI_API_KEY_5', ''),
        os.getenv('GEMINI_API_KEY_6', ''),
        os.getenv('GEMINI_API_KEY_7', ''),
        os.getenv('GEMINI_API_KEY_8', ''),
        os.getenv('GEMINI_API_KEY_9', ''),
        os.getenv('GEMINI_API_KEY_10', ''),
        
        # يمكنك إضافة المزيد هنا مباشرة (غير مستحسن للأمان)
        # 'AIzaSyExample1234567890abcdef',
        # 'AIzaSyExample0987654321fedcba',
    ]
    
    # إزالة المفاتيح الفارغة
    GEMINI_API_KEYS = [key.strip() for key in GEMINI_API_KEYS if key.strip()]
    
    # إعدادات الاستخدام الآمن
    USAGE_SETTINGS = {
        # الحد اليومي لكل مفتاح (طلبات)
        'daily_limit_per_key': 1000,
        
        # الحد الأقصى للطلبات في الدقيقة لكل مفتاح
        'requests_per_minute': 60,
        
        # وقت الانتظار بين الطلبات (ثواني)
        'request_delay': 1,
        
        # عدد المحاولات عند الفشل
        'max_retries': 3,
        
        # وقت انتظار الاستجابة (ثواني)
        'timeout': 30,
        
        # تفعيل التناوب التلقائي
        'auto_rotation': True,
        
        # تفعيل القائمة السوداء التلقائية
        'auto_blacklist': True,
    }
    
    # إعدادات الأمان
    SAFETY_SETTINGS = {
        # تجنب المحتوى الضار
        'avoid_harmful_content': True,
        
        # استخدام فقط للمحتوى الآمن
        'safe_content_only': True,
        
        # تجنب الطلبات المشبوهة
        'avoid_suspicious_requests': True,
        
        # الحد الأقصى لطول النص المُرسل
        'max_input_length': 8000,
        
        # الحد الأقصى لطول النص المُستقبل
        'max_output_length': 4000,
    }
    
    # أنواع الطلبات المسموحة (لتجنب الحظر)
    ALLOWED_REQUEST_TYPES = [
        'content_generation',      # توليد المحتوى
        'text_analysis',          # تحليل النص
        'translation',            # الترجمة
        'summarization',          # التلخيص
        'keyword_extraction',     # استخراج الكلمات المفتاحية
        'content_optimization',   # تحسين المحتوى
        'seo_analysis',          # تحليل SEO
        'content_categorization', # تصنيف المحتوى
    ]
    
    # أنواع الطلبات المحظورة (لتجنب مشاكل API)
    FORBIDDEN_REQUEST_TYPES = [
        'personal_data_processing',  # معالجة البيانات الشخصية
        'harmful_content',          # المحتوى الضار
        'spam_generation',          # إنشاء الرسائل المزعجة
        'fake_news',               # الأخبار المزيفة
        'copyright_violation',      # انتهاك حقوق الطبع
        'adult_content',           # المحتوى للبالغين
        'violence',                # العنف
        'hate_speech',             # خطاب الكراهية
    ]
    
    @classmethod
    def get_available_keys(cls) -> List[str]:
        """الحصول على المفاتيح المتاحة"""
        return cls.GEMINI_API_KEYS.copy()
    
    @classmethod
    def get_keys_count(cls) -> int:
        """عدد المفاتيح المتاحة"""
        return len(cls.GEMINI_API_KEYS)
    
    @classmethod
    def is_request_type_allowed(cls, request_type: str) -> bool:
        """فحص ما إذا كان نوع الطلب مسموح"""
        return request_type in cls.ALLOWED_REQUEST_TYPES
    
    @classmethod
    def is_request_type_forbidden(cls, request_type: str) -> bool:
        """فحص ما إذا كان نوع الطلب محظور"""
        return request_type in cls.FORBIDDEN_REQUEST_TYPES
    
    @classmethod
    def get_usage_limit(cls, limit_type: str) -> int:
        """الحصول على حد الاستخدام"""
        return cls.USAGE_SETTINGS.get(limit_type, 0)
    
    @classmethod
    def validate_key_format(cls, api_key: str) -> bool:
        """التحقق من صيغة مفتاح API"""
        if not api_key or not isinstance(api_key, str):
            return False
        
        # مفاتيح Google API تبدأ بـ AIza وطولها 39 حرف
        if api_key.startswith('AIza') and len(api_key) == 39:
            return True
        
        return False
    
    @classmethod
    def add_key(cls, api_key: str) -> bool:
        """إضافة مفتاح جديد"""
        if not cls.validate_key_format(api_key):
            return False
        
        if api_key not in cls.GEMINI_API_KEYS:
            cls.GEMINI_API_KEYS.append(api_key)
            return True
        
        return False
    
    @classmethod
    def remove_key(cls, api_key: str) -> bool:
        """إزالة مفتاح"""
        if api_key in cls.GEMINI_API_KEYS:
            cls.GEMINI_API_KEYS.remove(api_key)
            return True
        
        return False
    
    @classmethod
    def get_configuration_status(cls) -> Dict:
        """الحصول على حالة الإعدادات"""
        valid_keys = [key for key in cls.GEMINI_API_KEYS if cls.validate_key_format(key)]
        
        return {
            'total_keys': len(cls.GEMINI_API_KEYS),
            'valid_keys': len(valid_keys),
            'invalid_keys': len(cls.GEMINI_API_KEYS) - len(valid_keys),
            'configuration_complete': len(valid_keys) > 0,
            'recommended_minimum_keys': 3,
            'has_minimum_keys': len(valid_keys) >= 3,
            'safety_enabled': cls.SAFETY_SETTINGS['safe_content_only'],
            'auto_rotation_enabled': cls.USAGE_SETTINGS['auto_rotation']
        }

# دالة مساعدة لإعداد متغيرات البيئة
def setup_gemini_environment():
    """إعداد متغيرات البيئة لمفاتيح Gemini"""
    env_template = """
# مفاتيح Gemini API
# احصل على مفاتيح مجانية من: https://makersuite.google.com/app/apikey

# المفتاح الأساسي
GEMINI_API_KEY_1=your_first_gemini_key_here

# مفاتيح إضافية للتناوب (مستحسن)
GEMINI_API_KEY_2=your_second_gemini_key_here
GEMINI_API_KEY_3=your_third_gemini_key_here
GEMINI_API_KEY_4=your_fourth_gemini_key_here
GEMINI_API_KEY_5=your_fifth_gemini_key_here

# يمكنك إضافة المزيد حتى GEMINI_API_KEY_10
GEMINI_API_KEY_6=
GEMINI_API_KEY_7=
GEMINI_API_KEY_8=
GEMINI_API_KEY_9=
GEMINI_API_KEY_10=

# ملاحظات مهمة:
# 1. كل مفتاح له حد يومي مجاني
# 2. استخدم عدة مفاتيح لزيادة الحد اليومي
# 3. لا تشارك مفاتيحك مع أحد
# 4. احتفظ بنسخة احتياطية من مفاتيحك
"""
    
    # إنشاء ملف .env إذا لم يكن موجوداً
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_template)
        print("✅ تم إنشاء ملف .env لمفاتيح Gemini")
        print("📝 يرجى إضافة مفاتيح Gemini في ملف .env")
    else:
        print("⚠️ ملف .env موجود بالفعل")

def print_gemini_keys_status():
    """طباعة حالة مفاتيح Gemini"""
    print("🔑 حالة مفاتيح Gemini:")
    print("="*50)
    
    status = GeminiKeysConfig.get_configuration_status()
    
    print(f"📊 إجمالي المفاتيح: {status['total_keys']}")
    print(f"✅ مفاتيح صالحة: {status['valid_keys']}")
    print(f"❌ مفاتيح غير صالحة: {status['invalid_keys']}")
    print(f"🎯 الحد الأدنى المطلوب: {status['recommended_minimum_keys']}")
    print(f"📈 يحقق الحد الأدنى: {'نعم' if status['has_minimum_keys'] else 'لا'}")
    print(f"🔒 الأمان مفعل: {'نعم' if status['safety_enabled'] else 'لا'}")
    print(f"🔄 التناوب التلقائي: {'نعم' if status['auto_rotation_enabled'] else 'لا'}")
    
    if status['configuration_complete']:
        print("\n✅ الإعدادات مكتملة ومفاتيح Gemini جاهزة للاستخدام!")
    else:
        print("\n⚠️ يرجى إضافة مفاتيح Gemini صالحة في ملف .env")
        print("🔗 احصل على مفاتيح مجانية من: https://makersuite.google.com/app/apikey")

if __name__ == "__main__":
    # إعداد متغيرات البيئة
    setup_gemini_environment()
    
    # طباعة حالة المفاتيح
    print_gemini_keys_status()
